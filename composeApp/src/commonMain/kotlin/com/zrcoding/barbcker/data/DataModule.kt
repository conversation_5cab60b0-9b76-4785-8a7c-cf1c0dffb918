package com.zrcoding.barbcker.data

import com.zrcoding.barbcker.data.database.databaseModule
import com.zrcoding.barbcker.data.datastore.datastoreModule
import com.zrcoding.barbcker.data.repositories_impl.AccountRepositoryImpl
import com.zrcoding.barbcker.data.repositories_impl.AuthRepositoryImpl
import com.zrcoding.barbcker.data.repositories_impl.BarberRepositoryImpl
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.AuthRepository
import com.zrcoding.barbcker.domain.repositories.BarberRepository
import dev.gitlive.firebase.Firebase
import dev.gitlive.firebase.auth.FirebaseAuth
import dev.gitlive.firebase.auth.auth
import dev.gitlive.firebase.firestore.FirebaseFirestore
import dev.gitlive.firebase.firestore.firestore
import org.koin.core.module.dsl.factoryOf
import org.koin.dsl.bind
import org.koin.dsl.module

val dataModule = module {
    factoryOf(::AuthRepositoryImpl) bind (AuthRepository::class)
    factoryOf(::AccountRepositoryImpl) bind (AccountRepository::class)
    factoryOf(::BarberRepositoryImpl) bind (BarberRepository::class)
    factory<FirebaseAuth> { Firebase.auth }
    factory<FirebaseFirestore> { Firebase.firestore }
    includes(datastoreModule, databaseModule)
}