package com.zrcoding.barbcker.data.repositories_impl

import com.tweener.passage.Passage
import com.tweener.passage.model.Entrant
import com.zrcoding.barbcker.data.database.AppDatabase
import com.zrcoding.barbcker.data.dtos.AccountDto
import com.zrcoding.barbcker.data.dtos.ShopInfoDto
import com.zrcoding.barbcker.data.mappers.toBarberEntity
import com.zrcoding.barbcker.domain.models.AuthErrors
import com.zrcoding.barbcker.domain.models.AuthStatus
import com.zrcoding.barbcker.domain.models.Currency
import com.zrcoding.barbcker.domain.models.NetworkErrors
import com.zrcoding.barbcker.domain.models.Resource
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import com.zrcoding.barbcker.domain.repositories.AuthRepository
import dev.gitlive.firebase.FirebaseNetworkException
import dev.gitlive.firebase.auth.FirebaseAuth
import dev.gitlive.firebase.firestore.FirebaseFirestore

class AuthRepositoryImpl(
    private val passage: Passage,
    private val firebaseAuth: FirebaseAuth,
    private val firebaseFirestore: FirebaseFirestore,
    private val accountRepository: AccountRepository,
    private val appDatabase: AppDatabase,
) : AuthRepository {

    companion object {
        const val USERS_COLLECTION = "users"
    }

    override suspend fun authenticateWithGoogle(): Resource<AuthStatus, AuthErrors> {
        val result = passage.authenticateWithGoogle()
        return saveAccountAndHandleResult(result)
    }

    override suspend fun authenticateWithApple(): Resource<AuthStatus, AuthErrors> {
        val result = passage.authenticateWithApple()
        return saveAccountAndHandleResult(result)
    }

    override suspend fun completeAccount(
        name: String,
        currency: Currency
    ): Resource<Unit, AuthErrors> {
        return try {
            val user = firebaseAuth.currentUser
                ?: return Resource.Failure(AuthErrors.ShouldRelogIn)
            val info = ShopInfoDto(name, currency)
            val dto = hashMapOf("shop_info" to info)
            firebaseFirestore.collection(USERS_COLLECTION).document(user.uid).set(dto)
            Resource.Success(Unit)
        } catch (e: FirebaseNetworkException) {
            e.printStackTrace()
            Resource.Failure(AuthErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure(AuthErrors.Network(NetworkErrors.UNKNOWN))
        }
    }

    private suspend fun saveAccountAndHandleResult(
        result: Result<Entrant>
    ): Resource<AuthStatus, AuthErrors> {
        return try {
            val entrant = result.getOrThrow()
            val accountDto = firebaseFirestore
                .collection(USERS_COLLECTION)
                .document(entrant.uid)
                .get()
            if (accountDto.exists) {
                val (shopInfo, barbers) = accountDto.data(strategy = AccountDto.serializer())
                return if (shopInfo == null) {
                    accountRepository.saveAccount(entrant = entrant)
                    Resource.Success(AuthStatus.SHOULD_COMPLETE_ACCOUNT)
                } else {
                    accountRepository.saveAccount(
                        entrant = entrant,
                        shopName = shopInfo.shopName,
                        currency = shopInfo.currency
                    )
                    if (barbers.isEmpty()) {
                        Resource.Success(AuthStatus.SHOULD_SETUP_BARBERS)
                    } else {
                        appDatabase.barberDao().insertMany(
                            barbers = barbers.map { it.toBarberEntity() }
                        )
                        Resource.Success(AuthStatus.COMPLETED)
                    }
                }
            } else {
                accountRepository.saveAccount(entrant = entrant)
                Resource.Success(AuthStatus.SHOULD_COMPLETE_ACCOUNT)
            }
        } catch (e: FirebaseNetworkException) {
            e.printStackTrace()
            Resource.Failure(AuthErrors.Network(NetworkErrors.NO_INTERNET))
        } catch (e: Exception) {
            e.printStackTrace()
            Resource.Failure(AuthErrors.Network(NetworkErrors.UNKNOWN))
        }
    }
}