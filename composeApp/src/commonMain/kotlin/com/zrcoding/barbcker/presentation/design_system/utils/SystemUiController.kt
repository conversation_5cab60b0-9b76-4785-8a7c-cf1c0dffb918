package com.zrcoding.barbcker.presentation.design_system.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

/**
 * Common interface for controlling system UI elements like status bar
 */
expect class SystemUiController {
    fun setStatusBarColor(color: Color, darkIcons: <PERSON>olean)
}

@Composable
expect fun rememberSystemUiController(): SystemUiController
