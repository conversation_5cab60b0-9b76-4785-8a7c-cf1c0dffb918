package com.zrcoding.barbcker.presentation.features.barber.setup

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.NavigateNext
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.PersonAddAlt1
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import app.cash.paging.compose.collectAsLazyPagingItems
import app.cash.paging.compose.itemKey
import barbcker.composeapp.generated.resources.Res
import barbcker.composeapp.generated.resources.common_can_be_changed_later
import barbcker.composeapp.generated.resources.common_finish
import barbcker.composeapp.generated.resources.img_barber1
import barbcker.composeapp.generated.resources.img_barbers
import barbcker.composeapp.generated.resources.setup_barbers_add_barber_button
import barbcker.composeapp.generated.resources.setup_barbers_commission_rate
import barbcker.composeapp.generated.resources.setup_barbers_empty_state_description
import barbcker.composeapp.generated.resources.setup_barbers_title
import com.zrcoding.barbcker.domain.models.Barber
import com.zrcoding.barbcker.presentation.common.extension.isEmpty
import com.zrcoding.barbcker.presentation.design_system.components.BcPrimaryButton
import com.zrcoding.barbcker.presentation.design_system.components.BcTopAppBar
import com.zrcoding.barbcker.presentation.design_system.theme.dimension
import kotlinx.coroutines.flow.collectLatest
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import org.koin.compose.viewmodel.koinViewModel
import kotlin.uuid.ExperimentalUuidApi

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SetupBarbersRoute(
    navigateBack: () -> Unit,
    navigateToUpsertBarber: (Barber?) -> Unit,
    navigateToHome: () -> Unit,
    viewModel: SetupBarbersViewModel = koinViewModel()
) {
    val viewState = viewModel.viewState.collectAsStateWithLifecycle().value
    SetupBarbersScreen(
        viewState = viewState,
        onNavigateBack = navigateBack,
        onBarberClicked = { navigateToUpsertBarber(it) },
        onDeleteBarber = viewModel::onDeleteBarber,
        onFinishClicked = viewModel::onFinishClicked,
        onAddBarberClicked = { navigateToUpsertBarber(null) },
    )
    LaunchedEffect(Unit) {
        viewModel.oneTimeEvents.collectLatest {
            when (it) {
                SetupBarbersOneTimeEvents.NavigateToHome -> navigateToHome()
            }
        }
    }
}

@OptIn(ExperimentalUuidApi::class)
@Composable
private fun SetupBarbersScreen(
    viewState: SetupBarbersViewState,
    onNavigateBack: () -> Unit,
    onBarberClicked: (Barber) -> Unit,
    onDeleteBarber: (Barber) -> Unit,
    onFinishClicked: () -> Unit,
    onAddBarberClicked: () -> Unit,
) {
    val lazyPagingItems = viewState.barbers.collectAsLazyPagingItems()
    Scaffold(
        modifier = Modifier.fillMaxSize().safeDrawingPadding(),
        topBar = {
            BcTopAppBar(
                onNavigationIconClicked = onNavigateBack,
                title = stringResource(Res.string.setup_barbers_title)
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                lazyPagingItems.isEmpty() -> Column(
                    modifier = Modifier.fillMaxSize()
                        .padding(vertical = MaterialTheme.dimension.big)
                        .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large),
                ) {
                    Text(
                        text = stringResource(Res.string.setup_barbers_empty_state_description),
                        style = MaterialTheme.typography.titleMedium,
                    )
                    Image(
                        modifier = Modifier.fillMaxWidth(),
                        painter = painterResource(Res.drawable.img_barbers),
                        contentDescription = null,
                        contentScale = ContentScale.Crop
                    )
                }

                else -> LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(
                        start = MaterialTheme.dimension.screenPaddingHorizontal,
                        end = MaterialTheme.dimension.screenPaddingHorizontal,
                        bottom = MaterialTheme.dimension.extraExtraBig,
                        top = MaterialTheme.dimension.large
                    )
                ) {
                    items(
                        count = lazyPagingItems.itemCount,
                        key = lazyPagingItems.itemKey { it.uuid }
                    ) { index ->
                        lazyPagingItems[index]?.let { barber ->
                            BarberItem(
                                name = barber.name,
                                commissionRate = barber.commissionRate,
                                phoneNumber = barber.phoneNumber,
                                onClick = { onBarberClicked(barber) },
                                onDelete = { onDeleteBarber(barber) }
                            )
                        }
                    }
                }
            }
            Column(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .background(MaterialTheme.colorScheme.background)
                    .padding(top = MaterialTheme.dimension.medium)
                    .padding(horizontal = MaterialTheme.dimension.screenPaddingHorizontal),
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (lazyPagingItems.isEmpty().not()) {
                    Text(
                        text = stringResource(Res.string.common_can_be_changed_later),
                        style = MaterialTheme.typography.labelMedium,
                    )
                }
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    if (lazyPagingItems.isEmpty().not()) {
                        BcPrimaryButton(
                            modifier = Modifier.weight(1f),
                            text = stringResource(Res.string.common_finish),
                            trailingIcon = {
                                Icon(
                                    modifier = Modifier.size(MaterialTheme.dimension.bigger),
                                    imageVector = Icons.AutoMirrored.Default.NavigateNext,
                                    contentDescription = null
                                )
                            },
                            loading = viewState.isSubmitting,
                            onClick = onFinishClicked,
                        )
                        Spacer(modifier = Modifier.width(MaterialTheme.dimension.medium))
                    }
                    BcPrimaryButton(
                        text = stringResource(Res.string.setup_barbers_add_barber_button),
                        leadingIcon = {
                            Icon(
                                modifier = Modifier.size(MaterialTheme.dimension.bigger),
                                imageVector = Icons.Filled.PersonAddAlt1,
                                contentDescription = null
                            )
                        },
                        enabled = viewState.isSubmitting.not(),
                        onClick = onAddBarberClicked
                    )
                }
            }
        }
    }
}

@Composable
fun BarberItem(
    name: String,
    commissionRate: Int,
    phoneNumber: String,
    onClick: () -> Unit,
    onDelete: () -> Unit,
) {
    Row(
        modifier = Modifier
            .clickable(onClick = onClick)
            .padding(
                vertical = MaterialTheme.dimension.medium,
                horizontal = MaterialTheme.dimension.small
            )
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.large)
    ) {
        Image(
            modifier = Modifier.size(56.dp).clip(CircleShape),
            painter = painterResource(Res.drawable.img_barber1),
            contentDescription = "Barber avatar"
        )
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.small)
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.dimension.medium)
            ) {
                Text(text = name, style = MaterialTheme.typography.bodyLarge)
                Text(text = phoneNumber, style = MaterialTheme.typography.bodyLarge)
            }
            Text(
                text = stringResource(
                    Res.string.setup_barbers_commission_rate,
                    commissionRate
                ),
                style = MaterialTheme.typography.labelLarge
            )
        }
        Spacer(modifier = Modifier.weight(1f))
        IconButton(
            onClick = onDelete
        ) {
            Icon(
                imageVector = Icons.Default.Delete,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.error
            )
        }
    }
}