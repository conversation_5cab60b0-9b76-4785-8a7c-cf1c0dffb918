package com.zrcoding.barbcker.presentation.features.app

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.zrcoding.barbcker.domain.models.Account
import com.zrcoding.barbcker.domain.repositories.AccountRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.launch

class AppViewModel(
    private val accountRepository: AccountRepository,
) : ViewModel() {

    private val _startDestination = MutableStateFlow<StartDestination>(StartDestination.Idle)
    val startDestination = _startDestination.asStateFlow()

    init {
        viewModelScope.launch {
            val account = accountRepository.getAccount().firstOrNull()
            _startDestination.value = if (account == null || account == Account.NotConnected) {
                StartDestination.Screen.Auth
            } else {
                StartDestination.Screen.Home
            }
        }
    }
}