package com.zrcoding.barbcker.presentation.design_system.components

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BcTopAppBar(
    modifier: Modifier = Modifier,
    navigationIcon: ImageVector = Icons.AutoMirrored.Filled.ArrowBack,
    onNavigationIconClicked: () -> Unit,
    title: String? = null,
    actions: @Composable (RowScope.() -> Unit) = {}
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .statusBarsPadding()
            .height(TopAppBarDefaults.TopAppBarExpandedHeight),
        verticalAlignment = Alignment.CenterVertically
    ) {
        FilledIconButton(
            onClick = onNavigationIconClicked,
            colors = IconButtonDefaults.filledIconButtonColors(
                containerColor = MaterialTheme.colorScheme.surfaceContainerHighest,
            )
        ) {
            Icon(
                imageVector = navigationIcon,
                contentDescription = null
            )
        }
        title?.let {
            Text(
                modifier = Modifier.weight(1f),
                text = title,
                style = MaterialTheme.typography.labelLarge
            )
        }
        actions()
    }
}