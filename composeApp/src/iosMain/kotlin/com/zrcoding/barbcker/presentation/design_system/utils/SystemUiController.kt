package com.zrcoding.barbcker.presentation.design_system.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

actual class SystemUiController {
    actual fun setStatusBarColor(color: Color, darkIcons: Boolean) {
        // iOS status bar color is handled differently, typically through Info.plist or UIViewController
        // For now, this is a no-op implementation
    }
}

@Composable
actual fun rememberSystemUiController(): SystemUiController {
    return SystemUiController()
}
